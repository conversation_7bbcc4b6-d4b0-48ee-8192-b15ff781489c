<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Track stock prices and financial news with real-time data from MarketAux API">
    <title>📈 Stock Tracker - Real-time Financial News & Market Data</title>
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background-color: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .header h1 {
            font-size: 1.8rem;
            font-weight: 700;
        }

        .api-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .api-input-group {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .api-input {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.9rem;
            min-width: 250px;
        }

        .api-input:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
        }

        /* Main content */
        .main-content {
            padding: 2rem 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        /* Buttons */
        .btn-primary {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .btn-primary:hover:not(:disabled) {
            background: #2563eb;
        }

        .btn-primary:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.2s;
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .btn-small {
            background: #10b981;
            color: white;
            border: none;
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: background-color 0.2s;
        }

        .btn-small:hover {
            background: #059669;
        }

        .btn-remove {
            background: #ef4444;
            color: white;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: background-color 0.2s;
        }

        .btn-remove:hover {
            background: #dc2626;
        }

        /* Stock Search */
        .stock-search {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .search-input-group {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .search-input {
            flex: 1;
            padding: 0.75rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.2s;
        }

        .search-input:focus {
            outline: none;
            border-color: #3b82f6;
        }

        .search-results {
            border-top: 1px solid #e2e8f0;
            padding-top: 1rem;
        }

        .search-result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            transition: background-color 0.2s;
        }

        .search-result-item:hover {
            background-color: #f8fafc;
        }

        .entity-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .entity-details {
            font-size: 0.8rem;
            color: #64748b;
        }

        /* Content Grid */
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        /* Watchlist */
        .watchlist {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            height: fit-content;
        }

        .watchlist h2 {
            margin-bottom: 1rem;
            color: #1e293b;
            font-size: 1.3rem;
        }

        .stock-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .stock-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            background: #f1f5f9;
            border-radius: 8px;
            transition: background-color 0.2s;
        }

        .stock-item:hover {
            background: #e2e8f0;
        }

        .stock-symbol {
            font-weight: 600;
            color: #1e293b;
        }

        /* News Feed */
        .news-feed {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .news-feed h2 {
            margin-bottom: 1.5rem;
            color: #1e293b;
            font-size: 1.3rem;
        }

        .news-item {
            border-bottom: 1px solid #e2e8f0;
            padding-bottom: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .news-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .news-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.75rem;
            gap: 1rem;
        }

        .news-title {
            margin: 0;
            font-size: 1.1rem;
            line-height: 1.4;
        }

        .news-title a {
            color: #1e293b;
            text-decoration: none;
            transition: color 0.2s;
        }

        .news-title a:hover {
            color: #3b82f6;
        }

        .news-date {
            font-size: 0.8rem;
            color: #64748b;
            white-space: nowrap;
        }

        .news-description {
            color: #475569;
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        .news-entities {
            margin-bottom: 0.75rem;
            font-size: 0.9rem;
        }

        .entity-tag {
            display: inline-block;
            margin: 0.25rem 0.5rem 0.25rem 0;
            padding: 0.25rem 0.5rem;
            background: #f1f5f9;
            border-radius: 4px;
            font-size: 0.8rem;
        }

        .sentiment-indicator {
            margin-left: 0.5rem;
            font-weight: 500;
        }

        .news-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .news-source {
            font-size: 0.8rem;
            color: #64748b;
            font-style: italic;
        }

        /* Loading and Error States */
        .loading {
            text-align: center;
            padding: 2rem;
            color: #64748b;
            font-style: italic;
        }

        .error {
            text-align: center;
            padding: 2rem;
            color: #ef4444;
            background: #fef2f2;
            border-radius: 8px;
            border: 1px solid #fecaca;
        }

        .no-data {
            text-align: center;
            padding: 2rem;
            color: #64748b;
            font-style: italic;
        }

        /* API Notice */
        .api-notice {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            margin-top: 2rem;
        }

        .api-notice a {
            color: #3b82f6;
            text-decoration: none;
            font-weight: 500;
        }

        .api-notice a:hover {
            text-decoration: underline;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                text-align: center;
            }

            .api-input-group {
                flex-direction: column;
                width: 100%;
            }

            .api-input {
                min-width: auto;
                width: 100%;
            }

            .search-input-group {
                flex-direction: column;
            }

            .content-grid {
                grid-template-columns: 1fr;
            }

            .news-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .search-result-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
            }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div id="app">
        <header class="header">
            <div class="header-content">
                <h1>📈 Stock Tracker</h1>
                <div class="api-section">
                    <div id="api-input-group" class="api-input-group">
                        <input
                            type="password"
                            id="api-input"
                            placeholder="Enter MarketAux API Key"
                            class="api-input"
                        />
                        <button id="save-api-btn" class="btn-primary">Save</button>
                    </div>
                    <button id="api-settings-btn" class="btn-secondary hidden">⚙️ API Settings</button>
                </div>
            </div>
        </header>

        <main class="main-content">
            <div class="container">
                <div class="stock-search">
                    <div class="search-input-group">
                        <input
                            type="text"
                            id="search-input"
                            placeholder="Search stocks (e.g., AAPL, Tesla, Microsoft)"
                            class="search-input"
                        />
                        <button id="search-btn" class="btn-primary" disabled>Search</button>
                    </div>
                    <div id="search-results" class="search-results hidden"></div>
                </div>

                <div class="content-grid">
                    <div class="watchlist">
                        <h2>📊 Your Watchlist</h2>
                        <div id="stock-list" class="stock-list">
                            <p class="no-data">No stocks in your watchlist. Search and add some stocks above!</p>
                        </div>
                    </div>

                    <div class="news-feed">
                        <h2>📰 Latest News</h2>
                        <div id="news-list">
                            <div class="no-data">No news available. Add some stocks to track!</div>
                        </div>
                    </div>
                </div>

                <div id="api-notice" class="api-notice">
                    <p>
                        📝 To get started, you need a MarketAux API key. 
                        <a href="https://www.marketaux.com/documentation" target="_blank" rel="noopener noreferrer">
                            Sign up for free here
                        </a>
                    </p>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Application state
        let apiKey = localStorage.getItem('marketaux_api_key') || '';
        let watchedStocks = JSON.parse(localStorage.getItem('watched_stocks') || '[]');
        let news = [];
        let isLoading = false;

        // API functions
        const API_BASE_URL = 'https://api.marketaux.com/v1';

        async function fetchNews(symbols = '', limit = 10) {
            const params = new URLSearchParams({
                api_token: apiKey,
                language: 'en',
                limit: limit.toString(),
                must_have_entities: 'true'
            });
            
            if (symbols) {
                params.append('symbols', symbols);
                params.append('filter_entities', 'true');
            }
            
            const response = await fetch(`${API_BASE_URL}/news/all?${params}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        }

        async function searchEntities(search) {
            const params = new URLSearchParams({
                api_token: apiKey,
                search: search,
                limit: '10'
            });
            
            const response = await fetch(`${API_BASE_URL}/entity/search?${params}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        }

        // Utility functions
        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function getSentimentColor(score) {
            if (score > 0.1) return '#22c55e'; // green
            if (score < -0.1) return '#ef4444'; // red
            return '#6b7280'; // gray
        }

        function getSentimentText(score) {
            if (score > 0.1) return 'Positive';
            if (score < -0.1) return 'Negative';
            return 'Neutral';
        }

        // DOM manipulation functions
        function updateApiUI() {
            const apiInputGroup = document.getElementById('api-input-group');
            const apiSettingsBtn = document.getElementById('api-settings-btn');
            const apiNotice = document.getElementById('api-notice');
            const searchBtn = document.getElementById('search-btn');

            if (apiKey) {
                apiInputGroup.classList.add('hidden');
                apiSettingsBtn.classList.remove('hidden');
                apiNotice.classList.add('hidden');
                searchBtn.disabled = false;
            } else {
                apiInputGroup.classList.remove('hidden');
                apiSettingsBtn.classList.add('hidden');
                apiNotice.classList.remove('hidden');
                searchBtn.disabled = true;
            }
        }

        function renderWatchlist() {
            const stockList = document.getElementById('stock-list');
            
            if (watchedStocks.length === 0) {
                stockList.innerHTML = '<p class="no-data">No stocks in your watchlist. Search and add some stocks above!</p>';
                return;
            }

            stockList.innerHTML = watchedStocks.map(symbol => `
                <div class="stock-item">
                    <span class="stock-symbol">${symbol}</span>
                    <button class="btn-remove" onclick="removeStock('${symbol}')">✕</button>
                </div>
            `).join('');
        }

        function renderSearchResults(results) {
            const searchResults = document.getElementById('search-results');
            
            if (!results || results.length === 0) {
                searchResults.classList.add('hidden');
                return;
            }

            searchResults.classList.remove('hidden');
            searchResults.innerHTML = results.map((entity, index) => `
                <div class="search-result-item">
                    <div class="entity-info">
                        <strong>${entity.symbol}</strong> - ${entity.name}
                        <span class="entity-details">
                            ${entity.exchange_long || 'N/A'} | ${entity.type || 'N/A'} | ${entity.industry || 'N/A'}
                        </span>
                    </div>
                    <button class="btn-small" onclick="addStock('${entity.symbol}')">Add</button>
                </div>
            `).join('');
        }

        function renderNews() {
            const newsList = document.getElementById('news-list');
            
            if (isLoading) {
                newsList.innerHTML = '<div class="loading">Loading news...</div>';
                return;
            }

            if (!news || news.length === 0) {
                newsList.innerHTML = '<div class="no-data">No news available. Add some stocks to track!</div>';
                return;
            }

            newsList.innerHTML = news.map(article => `
                <div class="news-item">
                    <div class="news-header">
                        <h3 class="news-title">
                            <a href="${article.url}" target="_blank" rel="noopener noreferrer">
                                ${article.title}
                            </a>
                        </h3>
                        <span class="news-date">${formatDate(article.published_at)}</span>
                    </div>
                    
                    <p class="news-description">${article.description}</p>
                    
                    ${article.entities && article.entities.length > 0 ? `
                        <div class="news-entities">
                            <strong>Mentioned stocks:</strong>
                            ${article.entities.map(entity => `
                                <span class="entity-tag">
                                    ${entity.symbol} 
                                    <span class="sentiment-indicator" style="color: ${getSentimentColor(entity.sentiment_score)}">
                                        ${getSentimentText(entity.sentiment_score)} (${entity.sentiment_score.toFixed(2)})
                                    </span>
                                </span>
                            `).join('')}
                        </div>
                    ` : ''}
                    
                    <div class="news-footer">
                        <span class="news-source">${article.source}</span>
                    </div>
                </div>
            `).join('');
        }

        // Event handlers
        async function loadNews() {
            if (!apiKey || watchedStocks.length === 0) return;
            
            isLoading = true;
            renderNews();
            
            try {
                const symbols = watchedStocks.join(',');
                const newsData = await fetchNews(symbols, 20);
                news = newsData.data || [];
            } catch (error) {
                console.error('Error loading news:', error);
                news = [];
                document.getElementById('news-list').innerHTML = `<div class="error">Error loading news: ${error.message}</div>`;
            } finally {
                isLoading = false;
                renderNews();
            }
        }

        function saveApiKey() {
            const input = document.getElementById('api-input');
            apiKey = input.value.trim();
            if (apiKey) {
                localStorage.setItem('marketaux_api_key', apiKey);
                updateApiUI();
                loadNews();
            }
        }

        function showApiSettings() {
            const apiInputGroup = document.getElementById('api-input-group');
            const apiSettingsBtn = document.getElementById('api-settings-btn');
            const input = document.getElementById('api-input');
            
            input.value = apiKey;
            apiInputGroup.classList.remove('hidden');
            apiSettingsBtn.classList.add('hidden');
        }

        async function searchStocks() {
            const searchInput = document.getElementById('search-input');
            const searchTerm = searchInput.value.trim();
            
            if (!searchTerm || !apiKey) return;
            
            try {
                const results = await searchEntities(searchTerm);
                renderSearchResults(results.data || []);
            } catch (error) {
                console.error('Search failed:', error);
                renderSearchResults([]);
            }
        }

        function addStock(symbol) {
            if (!watchedStocks.includes(symbol)) {
                watchedStocks.push(symbol);
                localStorage.setItem('watched_stocks', JSON.stringify(watchedStocks));
                renderWatchlist();
                loadNews();
            }
        }

        function removeStock(symbol) {
            watchedStocks = watchedStocks.filter(s => s !== symbol);
            localStorage.setItem('watched_stocks', JSON.stringify(watchedStocks));
            renderWatchlist();
            loadNews();
        }

        // Event listeners
        document.getElementById('save-api-btn').addEventListener('click', saveApiKey);
        document.getElementById('api-settings-btn').addEventListener('click', showApiSettings);
        document.getElementById('search-btn').addEventListener('click', searchStocks);
        
        document.getElementById('api-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') saveApiKey();
        });
        
        document.getElementById('search-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') searchStocks();
        });

        // Initialize app
        updateApiUI();
        renderWatchlist();
        renderNews();
        
        if (apiKey && watchedStocks.length > 0) {
            loadNews();
        }
    </script>
</body>
</html>
